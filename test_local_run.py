#!/usr/bin/env python3
"""
测试 local_run.py 脚本的所有功能
"""

import os
import subprocess
import sys
import tempfile
from pathlib import Path

def run_command(cmd):
    """运行命令并返回结果"""
    print(f"运行命令: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        print(f"返回码: {result.returncode}")
        if result.stdout:
            print("标准输出:")
            print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("命令超时")
        return False
    except Exception as e:
        print(f"命令执行失败: {e}")
        return False

def test_help():
    """测试帮助信息"""
    print("\n" + "="*60)
    print("测试1: 帮助信息")
    print("="*60)
    return run_command(["python", "local_run.py", "--help"])

def test_single_sequence():
    """测试单条序列预测"""
    print("\n" + "="*60)
    print("测试2: 单条序列预测")
    print("="*60)
    
    sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
    output_file = "test_single_output.csv"
    
    success = run_command([
        "python", "local_run.py", 
        "--sequence", sequence,
        "--id", "test_protein",
        "--output", output_file
    ])
    
    if success and os.path.exists(output_file):
        print(f"✅ 输出文件已生成: {output_file}")
        # 清理文件
        os.remove(output_file)
        return True
    else:
        print("❌ 单条序列预测失败")
        return False

def test_fasta_batch():
    """测试FASTA批量预测"""
    print("\n" + "="*60)
    print("测试3: FASTA批量预测")
    print("="*60)
    
    # 创建测试FASTA文件
    fasta_content = """>test_protein_1
MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG
>test_protein_2
MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL
>test_protein_3
ATGAAAGTTCGTCGTGAACGTCTGAAATCGATCGTCCGTATCCTGGAACGTTCGAAAGAACCGGTTTCGGGCGCGCAACTGGAAGAACTGTCGGTTTCGCGTCAGGTTATCGTCCAGGATATCGCGTATCTGCGTTCGCTGGGCTATAA"""
    
    fasta_file = "test_input.fasta"
    output_file = "test_fasta_output.csv"
    
    with open(fasta_file, 'w') as f:
        f.write(fasta_content)
    
    success = run_command([
        "python", "local_run.py",
        "--fasta", fasta_file,
        "--output", output_file
    ])
    
    # 清理文件
    if os.path.exists(fasta_file):
        os.remove(fasta_file)
    
    if success and os.path.exists(output_file):
        print(f"✅ 输出文件已生成: {output_file}")
        os.remove(output_file)
        return True
    else:
        print("❌ FASTA批量预测失败")
        return False

def test_csv_batch():
    """测试CSV批量预测"""
    print("\n" + "="*60)
    print("测试4: CSV批量预测")
    print("="*60)
    
    # 创建测试CSV文件
    csv_content = """id,sequence
test_protein_1,MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG
test_protein_2,MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL"""
    
    csv_file = "test_input_batch.csv"
    output_file = "test_csv_output.csv"
    
    with open(csv_file, 'w') as f:
        f.write(csv_content)
    
    success = run_command([
        "python", "local_run.py",
        "--csv", csv_file,
        "--output", output_file
    ])
    
    # 清理文件
    if os.path.exists(csv_file):
        os.remove(csv_file)
    
    if success and os.path.exists(output_file):
        print(f"✅ 输出文件已生成: {output_file}")
        os.remove(output_file)
        return True
    else:
        print("❌ CSV批量预测失败")
        return False

def test_quiet_mode():
    """测试静默模式"""
    print("\n" + "="*60)
    print("测试5: 静默模式")
    print("="*60)
    
    sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
    output_file = "test_quiet_output.csv"
    
    success = run_command([
        "python", "local_run.py",
        "--sequence", sequence,
        "--quiet",
        "--output", output_file
    ])
    
    if success and os.path.exists(output_file):
        print(f"✅ 静默模式测试成功")
        os.remove(output_file)
        return True
    else:
        print("❌ 静默模式测试失败")
        return False

def main():
    """主测试函数"""
    print("🧪 ThermoFinder local_run.py 功能测试")
    print("="*60)
    
    # 检查是否在正确的目录
    if not os.path.exists("local_run.py"):
        print("❌ 找不到 local_run.py 文件，请在正确的目录下运行此测试")
        sys.exit(1)
    
    # 检查模型文件是否存在
    model_paths = [
        "ThermoSeq_r1.0/Second_Model/4_.pkl",
        "ThermoSeq_c1.0/Second_Model/4_.pkl"
    ]
    
    for path in model_paths:
        if not os.path.exists(path):
            print(f"❌ 找不到模型文件: {path}")
            print("请确保模型文件存在")
            sys.exit(1)
    
    print("✅ 环境检查通过")
    
    # 运行测试
    tests = [
        ("帮助信息", test_help),
        ("单条序列预测", test_single_sequence),
        ("FASTA批量预测", test_fasta_batch),
        ("CSV批量预测", test_csv_batch),
        ("静默模式", test_quiet_mode)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ 测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "="*60)
    print("测试结果总结")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！local_run.py 工作正常")
        return 0
    else:
        print("⚠️  部分测试失败，请检查问题")
        return 1

if __name__ == "__main__":
    sys.exit(main())
