#!/usr/bin/env python3
"""
CUDA环境配置脚本
配置CUDA路径并检查兼容性
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CUDAEnvironmentConfigurator:
    """CUDA环境配置器"""
    
    def __init__(self):
        self.cuda_paths = [
            "/usr/local/cuda-12.6",
            "/usr/local/cuda-12",
            "/usr/local/cuda"
        ]
        self.current_cuda_path = None
        self.system_cuda_version = None
        
    def detect_cuda_installation(self):
        """检测CUDA安装"""
        logger.info("=== 检测CUDA安装 ===")
        
        for cuda_path in self.cuda_paths:
            path = Path(cuda_path)
            if path.exists():
                logger.info(f"✅ 找到CUDA安装: {cuda_path}")
                
                # 检查版本
                version_file = path / "version.json"
                if version_file.exists():
                    try:
                        import json
                        with open(version_file, 'r') as f:
                            version_info = json.load(f)
                        self.system_cuda_version = version_info.get('cuda', {}).get('version', 'Unknown')
                        logger.info(f"  CUDA版本: {self.system_cuda_version}")
                    except:
                        pass
                
                # 检查关键文件
                lib_path = path / "lib64"
                include_path = path / "include"
                bin_path = path / "bin"
                
                if lib_path.exists() and include_path.exists() and bin_path.exists():
                    logger.info(f"  ✅ 完整安装: lib64, include, bin 都存在")
                    self.current_cuda_path = str(path)
                    return True
                else:
                    logger.warning(f"  ⚠️ 不完整安装: 缺少某些目录")
            else:
                logger.info(f"❌ 未找到: {cuda_path}")
        
        return False
    
    def check_current_environment(self):
        """检查当前环境变量"""
        logger.info("\n=== 检查当前环境变量 ===")
        
        cuda_home = os.environ.get('CUDA_HOME', '')
        cuda_path = os.environ.get('CUDA_PATH', '')
        ld_library_path = os.environ.get('LD_LIBRARY_PATH', '')
        path = os.environ.get('PATH', '')
        
        logger.info(f"CUDA_HOME: {cuda_home}")
        logger.info(f"CUDA_PATH: {cuda_path}")
        logger.info(f"LD_LIBRARY_PATH: {ld_library_path}")
        
        # 检查PATH中的CUDA
        cuda_in_path = any('cuda' in p.lower() for p in path.split(':'))
        logger.info(f"CUDA在PATH中: {cuda_in_path}")
        
        return {
            'cuda_home': cuda_home,
            'cuda_path': cuda_path,
            'ld_library_path': ld_library_path,
            'cuda_in_path': cuda_in_path
        }
    
    def check_pytorch_cuda_compatibility(self):
        """检查PyTorch与CUDA的兼容性"""
        logger.info("\n=== 检查PyTorch CUDA兼容性 ===")
        
        try:
            import torch
            pytorch_version = torch.__version__
            pytorch_cuda_version = torch.version.cuda
            cuda_available = torch.cuda.is_available()
            
            logger.info(f"PyTorch版本: {pytorch_version}")
            logger.info(f"PyTorch编译的CUDA版本: {pytorch_cuda_version}")
            logger.info(f"CUDA可用: {cuda_available}")
            
            if cuda_available:
                device_count = torch.cuda.device_count()
                logger.info(f"GPU设备数量: {device_count}")
                
                for i in range(device_count):
                    device_name = torch.cuda.get_device_name(i)
                    logger.info(f"  GPU {i}: {device_name}")
            
            # 检查版本兼容性
            if self.system_cuda_version and pytorch_cuda_version:
                system_major = self.system_cuda_version.split('.')[0]
                pytorch_major = pytorch_cuda_version.split('.')[0]
                
                if system_major != pytorch_major:
                    logger.warning(f"⚠️ 版本不匹配: 系统CUDA {self.system_cuda_version} vs PyTorch CUDA {pytorch_cuda_version}")
                    return False
                else:
                    logger.info("✅ CUDA版本兼容")
                    return True
            
            return cuda_available
            
        except ImportError:
            logger.error("❌ PyTorch未安装")
            return False
        except Exception as e:
            logger.error(f"❌ 检查PyTorch时出错: {e}")
            return False
    
    def configure_environment_variables(self):
        """配置环境变量"""
        logger.info("\n=== 配置环境变量 ===")
        
        if not self.current_cuda_path:
            logger.error("❌ 未找到有效的CUDA安装")
            return False
        
        # 设置环境变量
        cuda_path = self.current_cuda_path
        cuda_lib_path = f"{cuda_path}/lib64"
        cuda_bin_path = f"{cuda_path}/bin"
        cuda_include_path = f"{cuda_path}/include"
        
        # 更新当前会话的环境变量
        os.environ['CUDA_HOME'] = cuda_path
        os.environ['CUDA_PATH'] = cuda_path
        
        # 更新LD_LIBRARY_PATH
        current_ld_path = os.environ.get('LD_LIBRARY_PATH', '')
        if cuda_lib_path not in current_ld_path:
            new_ld_path = f"{cuda_lib_path}:{current_ld_path}" if current_ld_path else cuda_lib_path
            os.environ['LD_LIBRARY_PATH'] = new_ld_path
        
        # 更新PATH
        current_path = os.environ.get('PATH', '')
        if cuda_bin_path not in current_path:
            new_path = f"{cuda_bin_path}:{current_path}" if current_path else cuda_bin_path
            os.environ['PATH'] = new_path
        
        logger.info(f"✅ 设置 CUDA_HOME: {cuda_path}")
        logger.info(f"✅ 设置 CUDA_PATH: {cuda_path}")
        logger.info(f"✅ 更新 LD_LIBRARY_PATH: {os.environ['LD_LIBRARY_PATH']}")
        logger.info(f"✅ 更新 PATH: 添加了 {cuda_bin_path}")
        
        return True
    
    def generate_environment_script(self):
        """生成环境配置脚本"""
        logger.info("\n=== 生成环境配置脚本 ===")
        
        if not self.current_cuda_path:
            logger.error("❌ 未找到有效的CUDA安装")
            return False
        
        # 生成bash脚本
        bash_script = f"""#!/bin/bash
# CUDA环境配置脚本
# 自动生成于 CUDA环境配置器

export CUDA_HOME={self.current_cuda_path}
export CUDA_PATH={self.current_cuda_path}
export LD_LIBRARY_PATH={self.current_cuda_path}/lib64:$LD_LIBRARY_PATH
export PATH={self.current_cuda_path}/bin:$PATH

echo "✅ CUDA环境已配置"
echo "CUDA_HOME: $CUDA_HOME"
echo "CUDA版本: {self.system_cuda_version}"
"""
        
        with open("setup_cuda_env.sh", "w") as f:
            f.write(bash_script)
        
        # 设置执行权限
        os.chmod("setup_cuda_env.sh", 0o755)
        
        logger.info("✅ 生成了 setup_cuda_env.sh")
        
        # 生成Python脚本
        python_script = f"""#!/usr/bin/env python3
# CUDA环境配置 - Python版本
import os

def setup_cuda_environment():
    os.environ['CUDA_HOME'] = '{self.current_cuda_path}'
    os.environ['CUDA_PATH'] = '{self.current_cuda_path}'
    
    # 更新LD_LIBRARY_PATH
    current_ld_path = os.environ.get('LD_LIBRARY_PATH', '')
    cuda_lib_path = '{self.current_cuda_path}/lib64'
    if cuda_lib_path not in current_ld_path:
        os.environ['LD_LIBRARY_PATH'] = f"{{cuda_lib_path}}:{{current_ld_path}}"
    
    # 更新PATH
    current_path = os.environ.get('PATH', '')
    cuda_bin_path = '{self.current_cuda_path}/bin'
    if cuda_bin_path not in current_path:
        os.environ['PATH'] = f"{{cuda_bin_path}}:{{current_path}}"
    
    print("✅ CUDA环境已在Python中配置")
    return True

if __name__ == "__main__":
    setup_cuda_environment()
"""
        
        with open("setup_cuda_env.py", "w") as f:
            f.write(python_script)
        
        logger.info("✅ 生成了 setup_cuda_env.py")
        
        return True
    
    def test_cuda_functionality(self):
        """测试CUDA功能"""
        logger.info("\n=== 测试CUDA功能 ===")
        
        try:
            # 测试nvcc
            result = subprocess.run(['nvcc', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info("✅ nvcc 可用")
                logger.info(f"nvcc版本信息:\n{result.stdout}")
            else:
                logger.warning("⚠️ nvcc 不可用")
        except FileNotFoundError:
            logger.warning("⚠️ nvcc 未找到")
        
        # 测试PyTorch CUDA
        try:
            import torch
            
            if torch.cuda.is_available():
                logger.info("✅ PyTorch CUDA 可用")
                
                # 创建测试张量
                x = torch.randn(3, 3).cuda()
                y = torch.randn(3, 3).cuda()
                z = torch.matmul(x, y)
                
                logger.info("✅ CUDA张量运算测试成功")
                logger.info(f"测试结果形状: {z.shape}")
                
                return True
            else:
                logger.error("❌ PyTorch CUDA 不可用")
                return False
                
        except Exception as e:
            logger.error(f"❌ CUDA功能测试失败: {e}")
            return False
    
    def provide_recommendations(self):
        """提供建议"""
        logger.info("\n=== 建议和解决方案 ===")
        
        # 检查PyTorch版本兼容性
        try:
            import torch
            pytorch_cuda_version = torch.version.cuda
            
            if self.system_cuda_version:
                system_major = int(self.system_cuda_version.split('.')[0])
                pytorch_major = int(pytorch_cuda_version.split('.')[0])
                
                if system_major > pytorch_major:
                    logger.info("💡 建议升级PyTorch以匹配系统CUDA版本:")
                    logger.info(f"   当前PyTorch CUDA: {pytorch_cuda_version}")
                    logger.info(f"   系统CUDA: {self.system_cuda_version}")
                    logger.info("   升级命令:")
                    logger.info("   pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121")
                    
                elif system_major < pytorch_major:
                    logger.info("💡 PyTorch版本较新，应该向后兼容")
                    
                else:
                    logger.info("✅ PyTorch和系统CUDA版本兼容")
        except:
            pass
        
        logger.info("\n🔧 使用方法:")
        logger.info("1. 运行: source setup_cuda_env.sh  # 配置当前shell")
        logger.info("2. 或在Python中: exec(open('setup_cuda_env.py').read())")
        logger.info("3. 重启Python环境以应用更改")

def main():
    """主函数"""
    logger.info("🚀 开始CUDA环境配置")
    
    configurator = CUDAEnvironmentConfigurator()
    
    # 1. 检测CUDA安装
    if not configurator.detect_cuda_installation():
        logger.error("❌ 未找到有效的CUDA安装")
        return False
    
    # 2. 检查当前环境
    configurator.check_current_environment()
    
    # 3. 检查PyTorch兼容性
    pytorch_compatible = configurator.check_pytorch_cuda_compatibility()
    
    # 4. 配置环境变量
    if configurator.configure_environment_variables():
        logger.info("✅ 环境变量配置成功")
    
    # 5. 生成配置脚本
    if configurator.generate_environment_script():
        logger.info("✅ 配置脚本生成成功")
    
    # 6. 测试CUDA功能
    cuda_working = configurator.test_cuda_functionality()
    
    # 7. 提供建议
    configurator.provide_recommendations()
    
    # 总结
    logger.info("\n" + "="*60)
    logger.info("📊 配置总结")
    logger.info("="*60)
    logger.info(f"CUDA安装路径: {configurator.current_cuda_path}")
    logger.info(f"CUDA版本: {configurator.system_cuda_version}")
    logger.info(f"PyTorch兼容: {'✅' if pytorch_compatible else '❌'}")
    logger.info(f"CUDA功能: {'✅' if cuda_working else '❌'}")
    
    if cuda_working:
        logger.info("\n🎉 CUDA环境配置成功！")
        return True
    else:
        logger.info("\n⚠️ CUDA环境需要进一步配置")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ CUDA环境配置完成！")
        print("请运行: source setup_cuda_env.sh")
    else:
        print("\n⚠️ CUDA环境配置需要手动调整")
