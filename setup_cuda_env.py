#!/usr/bin/env python3
# CUDA环境配置 - Python版本
import os

def setup_cuda_environment():
    os.environ['CUDA_HOME'] = '/usr/local/cuda-12.6'
    os.environ['CUDA_PATH'] = '/usr/local/cuda-12.6'
    
    # 更新LD_LIBRARY_PATH
    current_ld_path = os.environ.get('LD_LIBRARY_PATH', '')
    cuda_lib_path = '/usr/local/cuda-12.6/lib64'
    if cuda_lib_path not in current_ld_path:
        os.environ['LD_LIBRARY_PATH'] = f"{cuda_lib_path}:{current_ld_path}"
    
    # 更新PATH
    current_path = os.environ.get('PATH', '')
    cuda_bin_path = '/usr/local/cuda-12.6/bin'
    if cuda_bin_path not in current_path:
        os.environ['PATH'] = f"{cuda_bin_path}:{current_path}"
    
    print("✅ CUDA环境已在Python中配置")
    return True

if __name__ == "__main__":
    setup_cuda_environment()
