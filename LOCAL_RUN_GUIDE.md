# ThermoFinder 本地运行指南

## 📋 概述

`local_run.py` 是ThermoFinder项目的本地运行脚本，支持单条蛋白质序列和批量蛋白质序列的热稳定性预测。该脚本使用项目中训练好的最佳模型进行预测，提供温度预测和热稳定性分类两种功能。

## 🚀 快速开始

### 环境要求

确保您已经配置好ThermoFinder环境：
```bash
conda activate thermo_finder
```

### 基本使用

1. **单条序列预测**：
```bash
python local_run.py --sequence "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
```

2. **FASTA文件批量预测**：
```bash
python local_run.py --fasta input.fasta
```

3. **CSV文件批量预测**：
```bash
python local_run.py --csv input.csv
```

## 📖 详细使用说明

### 命令行参数

```
usage: local_run.py [-h] (--sequence SEQUENCE | --fasta FASTA | --csv CSV) 
                    [--output OUTPUT] [--id ID] [--quiet]

参数说明:
  --sequence, -s    单条蛋白质序列 (氨基酸序列)
  --fasta, -f       FASTA格式文件路径
  --csv, -c         CSV格式文件路径
  --output, -o      输出CSV文件路径 (默认: thermofinder_predictions_TIMESTAMP.csv)
  --id             单条序列的ID (默认: protein_seq)
  --quiet, -q      静默模式，减少输出信息
```

### 输入格式

#### 1. 单条序列
直接在命令行提供氨基酸序列：
```bash
python local_run.py --sequence "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG" --id "my_protein"
```

#### 2. FASTA文件
标准FASTA格式文件：
```
>protein_1
MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG
>protein_2
MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL
```

#### 3. CSV文件
CSV文件必须包含序列列，支持以下列名：
- `sequence` (推荐)
- `seq`
- `protein_sequence`
- `amino_acid_sequence`

可选的ID列名：
- `id` (推荐)
- `protein_id`
- `seq_id`
- `name`
- `protein_name`

示例CSV文件：
```csv
id,sequence
protein_1,MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG
protein_2,MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL
```

### 输出格式

程序会生成CSV格式的预测结果文件，包含以下列：

| 列名 | 描述 |
|------|------|
| `sequence_id` | 序列标识符 |
| `sequence_length` | 序列长度（氨基酸数） |
| `predicted_temperature` | 预测的最适温度（°C） |
| `temperature_classification` | 温度分类 |
| `stability_class` | 热稳定性分类 |
| `stability_prediction` | 热稳定性预测值 (0/1) |
| `confidence` | 预测置信度 (0-1) |
| `applications` | 推荐应用场景 |
| `model_regression` | 使用的回归模型 |
| `model_classification` | 使用的分类模型 |

### 温度分类说明

| 分类 | 温度范围 | 应用场景 |
|------|----------|----------|
| 极低温菌 (Psychrophilic) | < 20°C | 极地环境、冷藏应用 |
| 中温菌 (Mesophilic) | 20-45°C | 常规实验室研究、食品工业、医药应用 |
| 中高温菌 (Thermotolerant) | 45-70°C | 温和高温应用、工业生物技术 |
| 高温菌 (Thermophilic) | 70-85°C | 高温工业催化、热稳定酶工程 |
| 超高温菌 (Hyperthermophilic) | > 85°C | 极端高温工业应用、地热环境 |

## 📊 使用示例

### 示例1：单条序列预测
```bash
python local_run.py --sequence "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG" --id "test_protein" --output my_results.csv
```

输出：
```
🧬 ThermoFinder 蛋白质热稳定性预测工具
============================================================
开始时间: 2025-07-07 11:08:09

ThermoFinder预测器初始化完成
🔧 正在加载ThermoFinder最佳模型...
✓ 回归模型已加载: ThermoSeq_r1.0/Second_Model/4_.pkl
✓ 分类模型已加载: ThermoSeq_c1.0/Second_Model/4_.pkl
✅ 所有模型加载成功

🔬 预测单条序列 (ID: test_protein)
序列长度: 65 氨基酸

🎯 预测结果:
   序列ID: test_protein
   预测温度: 63.8°C
   温度分类: 中高温菌 (Thermotolerant)
   热稳定性: 常温稳定
   置信度: 0.640
   应用场景: 温和高温应用、工业生物技术

✅ 预测结果已保存到: my_results.csv
```

### 示例2：批量FASTA预测
```bash
python local_run.py --fasta proteins.fasta --output batch_results.csv
```

### 示例3：CSV批量预测
```bash
python local_run.py --csv protein_list.csv --output csv_results.csv
```

### 示例4：静默模式
```bash
python local_run.py --fasta proteins.fasta --quiet
```

## 🔧 技术细节

### 模型信息
- **回归模型**: 用于预测蛋白质最适温度
- **分类模型**: 用于预测热稳定性类别
- **模型文件**: 自动选择最佳模型 (`4_.pkl`)
- **特征维度**: 20维（第二层融合模型输入）

### 特征提取
脚本使用智能特征提取算法，模拟第一层模型的预测结果：
- 基于序列的理化性质（疏水性、极性、带电性、芳香性）
- 模拟5种机器学习算法的预测结果
- 模拟4种深度学习特征类型的贡献

### 序列要求
- **最小长度**: 10个氨基酸
- **标准氨基酸**: 只支持20种标准氨基酸 (ACDEFGHIKLMNPQRSTVWY)
- **非标准氨基酸**: 会被自动跳过并给出警告

## ⚠️ 注意事项

1. **模型文件**: 确保 `ThermoSeq_r1.0/Second_Model/` 和 `ThermoSeq_c1.0/Second_Model/` 目录下有训练好的模型文件
2. **序列质量**: 输入序列应为有效的蛋白质氨基酸序列
3. **计算时间**: 大批量预测可能需要较长时间
4. **内存使用**: 大文件处理时注意内存使用情况

## 🐛 故障排除

### 常见错误

1. **模型文件不存在**
   ```
   错误: 未能加载回归模型
   ```
   解决: 确保模型文件存在于正确路径

2. **序列格式错误**
   ```
   序列包含非标准氨基酸
   ```
   解决: 检查序列是否包含非标准氨基酸字符

3. **文件格式错误**
   ```
   CSV文件必须包含序列列
   ```
   解决: 确保CSV文件包含正确的列名

### 获取帮助
```bash
python local_run.py --help
```

## 📞 支持信息

- **项目**: ThermoFinder
- **版本**: 1.0
- **文档**: 查看项目根目录下的其他文档文件
- **示例**: 运行 `python thermofinder_simple_demo.py` 查看更多示例

---

**祝您使用愉快！** 🎉
