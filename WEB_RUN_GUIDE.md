# ThermoFinder web_run.sh 使用指南

## 📋 概述

`web_run.sh` 是为Web环境设计的ThermoFinder运行脚本，专门用于CSV文件的批量蛋白质热稳定性预测。该脚本自动处理环境配置，确保在正确的工作目录和conda环境中运行。

## 🚀 主要特性

### ✅ 自动环境配置
- **工作目录**: 自动切换到 `/userfiles/codehub/thermo_finder/`
- **Conda环境**: 自动激活 `thermo_finder` 环境
- **依赖检查**: 验证所有必需的Python包
- **模型验证**: 检查训练好的模型文件是否存在

### ✅ 增强的CSV处理
- **保留原始信息**: 输出文件包含输入CSV的所有原始列
- **智能列识别**: 自动识别序列列和ID列的多种命名方式
- **错误处理**: 即使预测失败也保留原始信息
- **完整输出**: 原始信息 + 预测结果的完整合并

### ✅ 健壮的错误处理
- **环境检查**: 全面的环境和依赖验证
- **文件验证**: 输入文件存在性和格式检查
- **执行监控**: 实时监控预测过程
- **清理机制**: 自动环境清理

## 📖 使用方法

### 基本语法
```bash
./web_run.sh --csv INPUT_CSV --output OUTPUT_CSV [选项]
```

### 必需参数
- `--csv INPUT_CSV`: 输入的CSV文件路径
- `--output OUTPUT_CSV`: 输出的CSV文件路径

### 可选参数
- `--quiet`: 静默模式，减少输出信息
- `--help`: 显示帮助信息

## 📊 输入格式要求

### CSV文件结构
输入CSV文件必须包含蛋白质序列列，支持以下列名：
- `sequence` (推荐)
- `seq`
- `protein_sequence`
- `amino_acid_sequence`

### 可选ID列
支持以下ID列名（如果没有会自动生成）：
- `id` (推荐)
- `protein_id`
- `seq_id`
- `name`
- `protein_name`

### 示例输入文件

#### 简单格式
```csv
id,sequence
protein_1,MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG
protein_2,MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL
```

#### 复杂格式
```csv
protein_id,protein_name,organism,sequence,molecular_weight,description
P001,Alpha-amylase,Bacillus subtilis,MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG,7200,Starch-degrading enzyme
P002,Beta-glucanase,Thermotoga maritima,MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL,68000,Cell wall degrading enzyme
```

## 📈 输出格式

### 输出文件结构
输出CSV文件包含：
1. **原始输入信息**: 所有输入列都会以 `input_` 前缀保留
2. **预测结果**: 完整的热稳定性预测信息

### 输出列说明

#### 原始信息列（示例）
- `input_protein_id`: 原始蛋白质ID
- `input_protein_name`: 原始蛋白质名称
- `input_organism`: 原始生物体信息
- `input_sequence`: 原始序列
- `input_molecular_weight`: 原始分子量
- `input_description`: 原始描述

#### 预测结果列
- `sequence_id`: 序列标识符
- `sequence_length`: 序列长度（氨基酸数）
- `predicted_temperature`: 预测的最适温度（°C）
- `temperature_classification`: 温度分类
- `stability_class`: 热稳定性分类
- `stability_prediction`: 热稳定性预测值 (0/1)
- `confidence`: 预测置信度 (0-1)
- `applications`: 推荐应用场景
- `model_regression`: 使用的回归模型
- `model_classification`: 使用的分类模型

## 🎯 使用示例

### 示例1：基本使用
```bash
./web_run.sh --csv proteins.csv --output results.csv
```

### 示例2：静默模式
```bash
./web_run.sh --csv proteins.csv --output results.csv --quiet
```

### 示例3：绝对路径
```bash
./web_run.sh --csv /path/to/input.csv --output /path/to/output.csv
```

### 示例4：查看帮助
```bash
./web_run.sh --help
```

## 📊 运行示例

### 输入文件 (complex_input.csv)
```csv
protein_id,protein_name,organism,sequence,molecular_weight,description
P001,Alpha-amylase,Bacillus subtilis,MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG,7200,Starch-degrading enzyme
```

### 运行命令
```bash
./web_run.sh --csv complex_input.csv --output complex_output.csv
```

### 输出文件 (complex_output.csv)
```csv
input_protein_id,input_protein_name,input_organism,input_sequence,input_molecular_weight,input_description,sequence_id,sequence_length,predicted_temperature,temperature_classification,stability_class,stability_prediction,confidence,applications,model_regression,model_classification
P001,Alpha-amylase,Bacillus subtilis,MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG,7200,Starch-degrading enzyme,P001,65,63.8,中高温菌 (Thermotolerant),常温稳定,0,0.64,温和高温应用、工业生物技术,4_.pkl,4_.pkl
```

## ⚠️ 注意事项

### 环境要求
1. **工作目录**: 脚本必须在 `/userfiles/codehub/thermo_finder/` 目录下运行
2. **Conda环境**: 必须存在名为 `thermo_finder` 的conda环境
3. **模型文件**: 必须存在训练好的模型文件
4. **Python包**: 必须安装所有必需的依赖包

### 文件要求
1. **输入文件**: 必须是有效的CSV格式
2. **序列列**: 必须包含蛋白质序列列
3. **序列格式**: 只支持标准20种氨基酸
4. **文件权限**: 确保有读取输入文件和写入输出文件的权限

### 性能考虑
1. **处理时间**: 大文件可能需要较长时间
2. **内存使用**: 大文件处理时注意内存使用
3. **并发限制**: 避免同时运行多个实例

## 🐛 故障排除

### 常见错误及解决方案

#### 1. 环境错误
```
[ERROR] conda环境不存在: thermo_finder
```
**解决方案**: 确保已创建并配置好thermo_finder环境

#### 2. 文件错误
```
[ERROR] 输入文件不存在: input.csv
```
**解决方案**: 检查文件路径是否正确

#### 3. 模型错误
```
[ERROR] 模型文件不存在
```
**解决方案**: 确保模型文件存在于正确位置

#### 4. 权限错误
```
[ERROR] 无法切换到工作目录
```
**解决方案**: 检查目录权限和路径

### 调试技巧
1. **查看详细输出**: 不使用 `--quiet` 参数
2. **检查日志**: 查看脚本输出的详细信息
3. **验证环境**: 手动检查conda环境和依赖
4. **测试小文件**: 先用小文件测试功能

## 📞 技术支持

### 获取帮助
```bash
./web_run.sh --help
```

### 相关文档
- `LOCAL_RUN_GUIDE.md`: local_run.py详细使用指南
- `THERMOFINDER_COMPLETE_SETUP.md`: 完整配置文档
- `FINAL_INSTALLATION_SUMMARY.md`: 安装总结

### 项目信息
- **项目**: ThermoFinder
- **版本**: 1.0
- **环境**: Web运行环境
- **支持**: CSV批量处理

---

**祝您使用愉快！** 🎉
