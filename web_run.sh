#!/bin/bash

# ThermoFinder Web运行脚本
# 用于Web环境下的蛋白质热稳定性预测
#
# 使用方法:
#   ./web_run.sh --csv input.csv --output output.csv
#
# 作者: ThermoFinder Team
# 版本: 1.0

set -e  # 遇到错误立即退出

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
WORK_DIR="/userfiles/codehub/thermo_finder"
CONDA_ENV="thermo_finder"
PYTHON_SCRIPT="local_run.py"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    if [[ "$QUIET_MODE" != "true" ]]; then
        echo -e "${BLUE}[INFO]${NC} $1"
    fi
}

log_success() {
    if [[ "$QUIET_MODE" != "true" ]]; then
        echo -e "${GREEN}[SUCCESS]${NC} $1"
    fi
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 静默模式下的关键信息输出
log_quiet() {
    if [[ "$QUIET_MODE" == "true" ]]; then
        echo "$1"
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
ThermoFinder Web运行脚本

使用方法:
    $0 --csv INPUT_CSV --output OUTPUT_CSV [选项]

必需参数:
    --csv INPUT_CSV      输入的CSV文件路径
    --output OUTPUT_CSV  输出的CSV文件路径

可选参数:
    --quiet             静默模式，减少输出信息
    --help              显示此帮助信息

示例:
    $0 --csv proteins.csv --output results.csv
    $0 --csv /path/to/input.csv --output /path/to/output.csv --quiet

注意:
    - 输入CSV文件必须包含蛋白质序列列（sequence, seq, protein_sequence等）
    - 输出文件将包含原始输入信息和预测结果
    - 脚本会自动设置工作目录并激活conda环境

EOF
}

# 检查参数
check_arguments() {
    if [[ -z "$INPUT_CSV" ]]; then
        log_error "缺少必需参数: --csv"
        show_help
        exit 1
    fi

    if [[ -z "$OUTPUT_CSV" ]]; then
        log_error "缺少必需参数: --output"
        show_help
        exit 1
    fi

    # 检查输入文件是否存在
    if [[ ! -f "$INPUT_CSV" ]]; then
        log_error "输入文件不存在: $INPUT_CSV"
        exit 1
    fi

    # 检查输入文件是否为CSV格式
    if [[ ! "$INPUT_CSV" =~ \.csv$ ]]; then
        log_warning "输入文件可能不是CSV格式: $INPUT_CSV"
    fi

    # 确保输出文件是CSV格式
    if [[ ! "$OUTPUT_CSV" =~ \.csv$ ]]; then
        log_warning "输出文件将被设置为CSV格式"
        OUTPUT_CSV="${OUTPUT_CSV%.csv}.csv"
    fi
}

# 检查环境
check_environment() {
    log_info "检查运行环境..."

    # 检查工作目录
    if [[ ! -d "$WORK_DIR" ]]; then
        log_error "工作目录不存在: $WORK_DIR"
        exit 1
    fi

    # 检查Python脚本
    if [[ ! -f "$WORK_DIR/$PYTHON_SCRIPT" ]]; then
        log_error "Python脚本不存在: $WORK_DIR/$PYTHON_SCRIPT"
        exit 1
    fi

    # 检查模型文件
    local model_files=(
        "$WORK_DIR/ThermoSeq_r1.0/Second_Model/4_.pkl"
        "$WORK_DIR/ThermoSeq_c1.0/Second_Model/4_.pkl"
    )

    for model_file in "${model_files[@]}"; do
        if [[ ! -f "$model_file" ]]; then
            log_error "模型文件不存在: $model_file"
            exit 1
        fi
    done

    # 检查conda是否可用
    if ! command -v conda &> /dev/null; then
        log_error "conda命令不可用，请确保已安装Anaconda/Miniconda"
        exit 1
    fi

    # 检查conda环境是否存在
    if ! conda env list | grep -q "^$CONDA_ENV "; then
        log_error "conda环境不存在: $CONDA_ENV"
        if [[ "$QUIET_MODE" != "true" ]]; then
            log_info "请运行以下命令创建环境:"
            log_info "conda create -n $CONDA_ENV python=3.8"
        fi
        exit 1
    fi

    log_success "环境检查通过"
}

# 设置环境
setup_environment() {
    log_info "设置运行环境..."

    # 切换到工作目录
    cd "$WORK_DIR" || {
        log_error "无法切换到工作目录: $WORK_DIR"
        exit 1
    }
    log_info "工作目录: $(pwd)"

    # 初始化conda
    eval "$(conda shell.bash hook)"

    # 激活conda环境
    log_info "激活conda环境: $CONDA_ENV"
    conda activate "$CONDA_ENV" || {
        log_error "无法激活conda环境: $CONDA_ENV"
        exit 1
    }

    # 验证Python环境
    if [[ "$QUIET_MODE" != "true" ]]; then
        local python_version=$(python --version 2>&1)
        log_info "Python版本: $python_version"
    fi

    # 验证关键包
    local packages=("pandas" "numpy" "sklearn" "joblib" "Bio")
    local package_names=("pandas" "numpy" "scikit-learn" "joblib" "biopython")

    for i in "${!packages[@]}"; do
        local package="${packages[$i]}"
        local package_name="${package_names[$i]}"
        if ! python -c "import $package" 2>/dev/null; then
            log_error "缺少必需的Python包: $package_name"
            exit 1
        fi
    done

    log_success "环境设置完成"
}

# 运行预测
run_prediction() {
    log_info "开始蛋白质热稳定性预测..."
    log_info "输入文件: $INPUT_CSV"
    log_info "输出文件: $OUTPUT_CSV"

    # 构建命令
    local cmd="python $PYTHON_SCRIPT --csv \"$INPUT_CSV\" --output \"$OUTPUT_CSV\""

    if [[ "$QUIET_MODE" == "true" ]]; then
        cmd="$cmd --quiet"
    fi

    log_info "执行命令: $cmd"

    # 记录开始时间
    local start_time=$(date +%s)

    # 执行预测
    if eval "$cmd"; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))

        # 检查输出文件
        if [[ -f "$OUTPUT_CSV" ]]; then
            local line_count=$(wc -l < "$OUTPUT_CSV")

            if [[ "$QUIET_MODE" == "true" ]]; then
                # 静默模式下只输出关键结果
                log_quiet "SUCCESS: $OUTPUT_CSV"
            else
                # 正常模式下显示详细信息
                log_success "预测完成！"
                log_info "耗时: ${duration}秒"
                log_success "输出文件已生成: $OUTPUT_CSV"
                log_info "输出行数: $line_count"
            fi
        else
            log_error "输出文件未生成"
            exit 1
        fi
    else
        log_error "预测失败"
        exit 1
    fi
}

# 清理函数
cleanup() {
    if [[ -n "$CONDA_ENV" ]]; then
        if [[ "$QUIET_MODE" != "true" ]]; then
            log_info "清理环境..."
        fi
        conda deactivate 2>/dev/null || true
    fi
}

# 主函数
main() {
    # 设置清理陷阱
    trap cleanup EXIT

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --csv)
                INPUT_CSV="$2"
                shift 2
                ;;
            --output)
                OUTPUT_CSV="$2"
                shift 2
                ;;
            --quiet)
                QUIET_MODE="true"
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 在静默模式下不显示启动信息
    if [[ "$QUIET_MODE" != "true" ]]; then
        log_info "🧬 ThermoFinder Web运行脚本启动"
        log_info "================================================"
    fi

    # 检查参数
    check_arguments

    # 转换为绝对路径
    INPUT_CSV=$(realpath "$INPUT_CSV")
    OUTPUT_CSV=$(realpath "$OUTPUT_CSV")

    # 检查环境
    check_environment

    # 设置环境
    setup_environment

    # 运行预测
    run_prediction

    # 在静默模式下不显示完成信息
    if [[ "$QUIET_MODE" != "true" ]]; then
        log_success "🎉 所有任务完成！"
        log_info "结果文件: $OUTPUT_CSV"
    fi
}

# 运行主函数
main "$@"